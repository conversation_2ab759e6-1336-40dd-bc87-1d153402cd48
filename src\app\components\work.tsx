'use client';
import React, { useRef } from "react";
import { useTransform, motion, useScroll, MotionValue } from 'framer-motion';

const ORANGE = "#e8561c";

const workImages = [
  '/img/work/Screenshot 2025-06-28 164306.png',
  '/img/work/Screenshot 2025-06-28 164452.png',
  '/img/work/Screenshot 2025-06-28 164758.png'
];

interface ImageCardProps {
  i: number;
  src: string;
  progress: MotionValue<number>;
  range: [number, number];
  targetScale: number;
}

const ImageCard: React.FC<ImageCardProps> = ({
  i,
  src,
  progress,
  range,
  targetScale,
}) => {
  const container = useRef(null);
  const { scrollYProgress } = useScroll({
    target: container,
    offset: ['start end', 'start start'],
  });

  const scale = useTransform(progress, range, [1, targetScale]);

  return (
    <div
      ref={container}
      style={{
        height: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        position: 'sticky',
        top: 0,
      }}
    >
      <motion.div
        style={{
          scale,
          top: `calc(5vh + ${i * 25}px)`,
          position: 'relative',
          width: '1400px',
          height: '750px',
          borderRadius: '25px',
          overflow: 'hidden',
          boxShadow: '0 25px 50px rgba(0,0,0,0.2)',
        }}
      >
        <img 
          src={src} 
          alt={`Work project ${i + 1}`}
          style={{
            width: '100%',
            height: '100%',
            objectFit: 'cover',
          }}
        />
      </motion.div>
    </div>
  );
};

const Work = () => {
  const container = useRef(null);
  const { scrollYProgress } = useScroll({
    target: container,
    offset: ['start start', 'end end'],
  });

  return (
    <section
      ref={container}
      style={{
        background: "#f0f0f0",
        width: "100%",
        minHeight: "100vh",
        fontFamily: 'Satoshi, Arial, Helvetica, sans-serif',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        position: 'relative',
      }}
    >
      {/* Top label - WORK at very left of section */}
      <div style={{
        position: 'absolute',
        top: 120,
        left: 40,
        color: '#000',
        fontSize: 28,
        fontWeight: 700,
        letterSpacing: 1.5,
        zIndex: 10,
      }}>
        (WORK)
      </div>
      {/* Right label - 02 at very right of section */}
      <div style={{
        position: 'absolute',
        top: '600px',
        right: 40,
        color: ORANGE,
        fontSize: 54,
        fontWeight: 700,
        letterSpacing: 1.5,
        zIndex: 10,
      }}>
        (02)
      </div>

      <div style={{
        maxWidth: 1200,
        margin: '0 auto',
        padding: 0,
        width: '100%',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        minHeight: '100vh',
        marginTop: 80,
        position: 'relative',
      }}>
        {/* Main content with left padding reduced */}
        <div style={{ padding: '0 40px 0 20px' }}>
          {/* Intro - much larger */}
          <div style={{
            color: '#000',
            fontSize: 64,
            fontWeight: 700,
            margin: '180px 0 80px 0',
            maxWidth: 900,
            alignSelf: 'flex-start',
            lineHeight: 1.15,
            marginLeft: -150,
          }}>
            Selected Work<br />
            <span style={{
              fontWeight: 400,
              fontSize: 40,
              display: 'block',
              marginTop: 32,
              lineHeight: 1.2,
            }}>
              A collection of projects that showcase my approach to solving complex problems through thoughtful design and clean code.
            </span>
          </div>

          {/* CSS Sticky Stacking Images Section */}
          <div style={{
            width: '100%',
            background: '#f0f0f0',
          }}>
            <div className="sticky top-0 w-full">
              <figure className="w-full h-screen flex items-center justify-center">
                <img
                  src={workImages[0]}
                  alt="Work project 1"
                  className="transition-all duration-300 h-[450px] w-[70%] object-cover rounded-md"
                  style={{
                    boxShadow: '0 25px 50px rgba(0,0,0,0.2)',
                  }}
                />
              </figure>
            </div>
            <div className="sticky top-2 w-full">
              <figure className="w-full h-screen flex items-center justify-center">
                <img
                  src={workImages[1]}
                  alt="Work project 2"
                  className="transition-all duration-300 h-[450px] w-[70%] object-cover rounded-md"
                  style={{
                    boxShadow: '0 25px 50px rgba(0,0,0,0.2)',
                  }}
                />
              </figure>
            </div>
            <div className="sticky top-4 w-full">
              <figure className="w-full h-screen flex items-center justify-center">
                <img
                  src={workImages[2]}
                  alt="Work project 3"
                  className="transition-all duration-300 h-[450px] w-[70%] object-cover rounded-md"
                  style={{
                    boxShadow: '0 25px 50px rgba(0,0,0,0.2)',
                  }}
                />
              </figure>
            </div>
          </div>

          {/* Bottom spacing */}
          <div style={{
            height: '100vh',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}>
            <div style={{
              textAlign: 'center',
              color: '#666',
              fontSize: 18,
              fontWeight: 500,
            }}>
              Scroll to explore more work
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Work;
