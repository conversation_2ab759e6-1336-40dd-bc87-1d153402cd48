'use client';
import React from "react";

const ORANGE = "#e8561c";

const workImages = [
  '/img/work/Screenshot 2025-06-28 164306.png',
  '/img/work/Screenshot 2025-06-28 164452.png',
  '/img/work/Screenshot 2025-06-28 164758.png'
];

const Work = () => {
  return (
    <>
      <section
        style={{
          background: "#f0f0f0",
          width: "100%",
          minHeight: "100vh",
          fontFamily: 'Satoshi, Arial, Helvetica, sans-serif',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          position: 'relative',
        }}
      >
        {/* Top label - WORK at very left of section */}
        <div style={{
          position: 'absolute',
          top: 120,
          left: 40,
          color: '#000',
          fontSize: 28,
          fontWeight: 700,
          letterSpacing: 1.5,
          zIndex: 10,
        }}>
          (WORK)
        </div>
        {/* Right label - 02 at very right of section */}
        <div style={{
          position: 'absolute',
          top: '600px',
          right: 40,
          color: ORANGE,
          fontSize: 54,
          fontWeight: 700,
          letterSpacing: 1.5,
          zIndex: 10,
        }}>
          (02)
        </div>

        <div style={{
          maxWidth: 1200,
          margin: '0 auto',
          padding: 0,
          width: '100%',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          minHeight: '100vh',
          marginTop: 80,
          position: 'relative',
        }}>
          {/* Main content with left padding reduced */}
          <div style={{ padding: '0 40px 0 20px' }}>
            {/* Intro - much larger */}
            <div style={{
              color: '#000',
              fontSize: 64,
              fontWeight: 700,
              margin: '180px 0 80px 0',
              maxWidth: 900,
              alignSelf: 'flex-start',
              lineHeight: 1.15,
              marginLeft: -150,
            }}>
              Selected Work<br />
              <span style={{
                fontWeight: 400,
                fontSize: 40,
                display: 'block',
                marginTop: 32,
                lineHeight: 1.2,
              }}>
                A collection of projects that showcase my approach to solving complex problems through thoughtful design and clean code.
              </span>
            </div>
          </div>
        </div>
      </section>

      {/* EXAKT WIE DEIN CODE - Section mit bg-slate-950 */}
      <section className="text-white w-full bg-slate-950">
        <>
          <div className="sm:sticky sm:top-0 w-full">
            <figure className="w-full h-screen flex items-center justify-center">
              <img
                src={workImages[0]}
                alt=""
                className="transition-all duration-300 h-[80%] w-[55%] align-bottom object-cover rounded-md"
              />
            </figure>
          </div>
          <div className="sm:sticky sm:top-2 w-full">
            <figure className="w-full h-screen flex items-center justify-center">
              <img
                src={workImages[1]}
                alt=""
                className="transition-all duration-300 h-[80%] w-[60%] align-bottom object-cover rounded-md"
              />
            </figure>
          </div>
          <div className="sm:sticky sm:top-4 w-full">
            <figure className="w-full h-screen flex items-center justify-center">
              <img
                src={workImages[2]}
                alt=""
                className="transition-all duration-300 h-[80%] w-[65%] align-bottom object-cover rounded-md"
              />
            </figure>
          </div>
        </>
      </section>

      {/* Bottom spacing */}
      <div style={{
        height: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        background: '#f0f0f0',
      }}>
        <div style={{
          textAlign: 'center',
          color: '#666',
          fontSize: 18,
          fontWeight: 500,
        }}>
          Scroll to explore more work
        </div>
      </div>
    </>
  );
};

export default Work;
